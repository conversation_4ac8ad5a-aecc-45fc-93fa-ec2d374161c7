import { NextRequest, NextResponse } from 'next/server';
import { handleMongoDBError } from '@/app/lib/mongodb';
import { StockMovementService, StockMovementRequest, StockMovementType } from '@/app/services/stockmovement.service';
import { InvalidParameterError } from '@/app/lib/errors';

// Legacy interface for backward compatibility
interface StockUpdateRequest {
  itemId: string; // Renamed from partId for clarity
  itemType: 'Part' | 'Assembly' | 'Product'; // Added
  warehouseId: string;
  quantityChange: number;
  transactionType?: string; // Renamed from 'type'
  notes?: string;
  userId: string;
  referenceId?: string;
  referenceModel?: string;
}

/**
 * POST handler for updating stock levels
 * PHASE 1: Consolidated to use StockMovementService for unified transaction processing
 * Maintains backward compatibility with legacy request/response format
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();

  try {
    console.log('[API] POST /api/inventory/update-stock - Updating stock level (Phase 1: Consolidated)');
    const data = await request.json() as StockUpdateRequest;

    // Validate required fields
    if (!data.itemId || !data.itemType || !data.warehouseId || data.quantityChange === undefined || !data.userId) {
      throw new InvalidParameterError('Missing required fields: itemId, itemType, warehouseId, quantityChange, userId are required.');
    }

    if (!['Part', 'Assembly', 'Product'].includes(data.itemType)) {
      throw new InvalidParameterError(`Invalid itemType: ${data.itemType}. Must be 'Part', 'Assembly', or 'Product'.`);
    }

    if (typeof data.quantityChange !== 'number') {
        throw new InvalidParameterError('quantityChange must be a number.');
    }

    console.log('[API] Phase 1: Transforming legacy request to StockMovementRequest');

    // Transform legacy request to modern StockMovementRequest
    const stockMovementRequest: StockMovementRequest = {
      partId: data.itemId,
      movementType: 'adjustment' as StockMovementType,
      quantity: Math.abs(data.quantityChange),
      from: data.quantityChange < 0 ? {
        warehouseId: data.warehouseId,
        stockType: 'finished' // Default to finished stock
      } : null,
      to: data.quantityChange > 0 ? {
        warehouseId: data.warehouseId,
        stockType: 'finished' // Default to finished stock
      } : null,
      notes: data.notes || `Stock ${data.quantityChange > 0 ? 'increase' : 'decrease'} via update-stock API`,
      userId: data.userId,
      transactionDate: new Date(),
      ...(data.referenceId && { referenceNumber: data.referenceId }),
      ...(data.referenceModel && {
        referenceType: data.referenceModel as 'PurchaseOrder' | 'WorkOrder' | 'SalesOrder' | 'StockAdjustment' | 'ProcessOrder'
      })
    };

    console.log('[API] Phase 1: Executing stock movement via StockMovementService');

    // Execute the stock movement using the consolidated service
    const result = await StockMovementService.executeMovement(stockMovementRequest);

    const duration = Date.now() - startTime;
    console.log(`[API] Phase 1: Stock update completed successfully in ${duration}ms`);

    // Transform response to maintain backward compatibility
    return NextResponse.json({
      success: true,
      data: {
        inventory: result.updatedPart, // Maintain legacy field name
        transaction: result.transaction
      },
      meta: {
        duration,
        message: result.message
      }
    });

  } catch (error: any) {
    const duration = Date.now() - startTime;
    console.error(`[API] Phase 1: Error in POST /api/inventory/update-stock (${duration}ms):`, error);

    if (error instanceof InvalidParameterError) {
        return NextResponse.json(
            { success: false, error: error.message, details: error.details },
            { status: 400 }
        );
    }

    // Handle specific error types
    if (error.message.includes('Insufficient stock') ||
        error.message.includes('Part') ||
        error.message.includes('Warehouse') ||
        error.message.includes('Invalid') ||
        error.message.includes('Unsupported')) {
      return NextResponse.json({ success: false, error: error.message }, { status: 400 });
    }

    // Attempt to handle MongoDB specific errors, otherwise use generic error message
    let errorMessage = error.message || 'Failed to update stock';
    if (typeof handleMongoDBError === 'function' && error.name && (error.name.includes('Mongo') || error.code)) {
        errorMessage = handleMongoDBError(error) || errorMessage;
    }

    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: error.status || 500 }
    );
  }
}