import { successResponse } from '@/app/lib/api-response';
import { InvalidParameterError } from '@/app/lib/errors';
import withErrorHandling from '@/app/middlewares/withErrorHandling';
import { getInventoryById } from '@/app/services/inventory.service';
import { StockMovementService, StockMovementRequest, StockMovementType } from '@/app/services/stockmovement.service';
import { logApiRequest } from '@/app/services/logging';
import mongoose from 'mongoose';
import { NextRequest, NextResponse } from 'next/server';

const ROUTE_PATH = '/api/inventory/adjust';

/**
 * POST handler for adjusting inventory quantities
 * PHASE 1: Consolidated to use StockMovementService for unified transaction processing
 * Maintains backward compatibility with legacy request/response format
 * @param request - The incoming request with adjustment data
 * @returns JSON response with the updated inventory record
 */
async function handlePOST(request: NextRequest) {
  const startTime = Date.now();

  // Log API request
  await logApiRequest('POST', ROUTE_PATH, null, true);

  console.log('[API] POST /api/inventory/adjust - Adjusting inventory quantity (Phase 1: Consolidated)');
  const adjustmentData = await request.json() as {
    id?: string;
    adjustment_quantity?: number; // Legacy snake_case field name
    adjustmentQuantity?: number;  // Modern camelCase alternative
    reason?: string;
    [key: string]: any;
  };

  // Basic validation
  if (!adjustmentData || typeof adjustmentData !== 'object') {
    throw new InvalidParameterError('Invalid adjustment data provided');
  }

  // Support both legacy snake_case and modern camelCase field names
  const adjustmentQuantity = adjustmentData.adjustment_quantity ?? adjustmentData.adjustmentQuantity;

  // Validate required fields
  if (!adjustmentData.id || adjustmentQuantity === undefined || !adjustmentData.reason) {
    throw new InvalidParameterError('Missing required fields: id, adjustment_quantity (or adjustmentQuantity), and reason are required');
  }
  
  // Validate ID format
  const { id } = adjustmentData;
  if (!id || !mongoose.Types.ObjectId.isValid(id)) {
    throw new InvalidParameterError(
      `Invalid inventory record ID format: ${id}`,
      [{ field: 'id', message: 'Invalid ID format' }]
    );
  }

  // Type assertion after validation
  const validId = id as string;

  // Check if inventory record exists
  const inventoryRecord = await getInventoryById(validId);
  if (!inventoryRecord) {
    return NextResponse.json(
      { data: null, error: `Inventory record with ID ${id} not found` },
      { status: 404 }
    );
  }

  // Validate adjustmentQuantity is a number
  if (typeof adjustmentQuantity !== 'number') {
    throw new InvalidParameterError(
      'adjustment_quantity (or adjustmentQuantity) must be a number',
      [{ field: 'adjustment_quantity', message: 'Must be a number' }]
    );
  }

  console.log(`[API] Phase 1: Transforming inventory adjustment to StockMovementRequest`);

  // Transform inventory adjustment to StockMovementRequest
  const stockMovementRequest: StockMovementRequest = {
    partId: inventoryRecord.partId.toString(),
    movementType: 'adjustment' as StockMovementType,
    quantity: Math.abs(adjustmentQuantity),
    from: adjustmentQuantity < 0 ? {
      warehouseId: inventoryRecord.warehouseId?.toString() || '',
      stockType: inventoryRecord.stockType
    } : null,
    to: adjustmentQuantity > 0 ? {
      warehouseId: inventoryRecord.warehouseId?.toString() || '',
      stockType: inventoryRecord.stockType
    } : null,
    notes: adjustmentData.reason as string,
    userId: '6751b8b8e5b8b8b8b8b8b8b8', // Default user ID - should be from session
    transactionDate: new Date(),
    referenceType: 'StockAdjustment'
  };

  console.log(`[API] Phase 1: Executing stock movement via StockMovementService`);

  // Execute the stock movement using the consolidated service
  const result = await StockMovementService.executeMovement(stockMovementRequest);

  const duration = Date.now() - startTime;
  console.log(`[API] Phase 1: Stock adjustment completed successfully in ${duration}ms`);

  // Get updated inventory record for response
  const updatedInventory = await getInventoryById(validId);

  return successResponse(
    updatedInventory,
    'Inventory quantity adjusted successfully',
    {
      duration,
      previous_quantity: inventoryRecord.quantity,
      new_quantity: updatedInventory?.quantity || inventoryRecord.quantity,
      adjustment: adjustmentQuantity,
      transaction: result.transaction
    }
  );
}

// Apply the withErrorHandling middleware to our handler
export const POST = withErrorHandling(handlePOST, ROUTE_PATH); 